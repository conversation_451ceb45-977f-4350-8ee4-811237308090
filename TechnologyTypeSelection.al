page 50133 "Technology Type Selection"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Technology Type";
    Caption = 'Select Technology Types';
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(Selected; Rec.Selected)
                {
                    ApplicationArea = All;
                    Caption = 'Select';
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    Caption = 'Technology Type';
                    Editable = false;
                }
            }
        }
    }
}