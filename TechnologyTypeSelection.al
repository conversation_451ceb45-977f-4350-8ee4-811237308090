page 50133 "Technology Type Selection"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Technology Type";
    SourceTableTemporary = true;
    Caption = 'Select Technology Types';
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(Selected; Rec.Selected)
                {
                    ApplicationArea = All;
                    Caption = 'Select';
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    Caption = 'Technology Type';
                    Editable = false;
                }
            }
        }
    }

    procedure SetTempRecord(var TempTechnologyType: Record "Technology Type" temporary)
    begin
        Rec.Reset();
        Rec.DeleteAll();

        if TempTechnologyType.FindSet() then
            repeat
                Rec := TempTechnologyType;
                if not Rec.Insert() then
                    Rec.Modify();
            until TempTechnologyType.Next() = 0;

        Rec.Reset();
        CurrPage.Update(false);
    end;

    procedure GetSelectedTypes(var TempTechnologyType: Record "Technology Type" temporary)
    begin
        TempTechnologyType.Reset();
        TempTechnologyType.DeleteAll();

        if Rec.FindSet() then
            repeat
                if Rec.<PERSON> then begin
                    TempTechnologyType := Rec;
                    TempTechnologyType.Insert();
                end;
            until Rec.Next() = 0;
    end;
}