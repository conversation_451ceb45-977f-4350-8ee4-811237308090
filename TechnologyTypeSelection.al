page 50133 "Technology Type Selection"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Technology Type";
    SourceTableTemporary = true;
    Caption = 'Select Technology Types';
    InsertAllowed = false;
    DeleteAllowed = false;
    Editable = true;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(Selected; Rec.Selected)
                {
                    ApplicationArea = All;
                    Caption = 'Select';
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    Caption = 'Technology Type';
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(SelectAll)
            {
                Caption = 'Select All';
                ApplicationArea = All;
                trigger OnAction()
                begin
                    if Rec.FindSet() then
                        repeat
                            Rec.Selected := true;
                            Rec.Modify();
                        until Rec.Next() = 0;
                    CurrPage.Update();
                end;
            }
            action(ClearAll)
            {
                Caption = 'Clear All';
                ApplicationArea = All;
                trigger OnAction()
                begin
                    if Rec.FindSet() then
                        repeat
                            Rec.Selected := false;
                            Rec.Modify();
                        until Rec.Next() = 0;
                    CurrPage.Update();
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        CurrPage.Editable := true;
    end;

    procedure SetTempRecord(var TempTechnologyType: Record "Technology Type" temporary)
    begin
        Rec.Reset();
        Rec.DeleteAll();

        if TempTechnologyType.FindSet() then
            repeat
                Rec := TempTechnologyType;
                if not Rec.Insert() then
                    Rec.Modify();
            until TempTechnologyType.Next() = 0;

        Rec.Reset();
        CurrPage.Update(false);
    end;

    procedure GetSelectedTypes(var TempTechnologyType: Record "Technology Type" temporary)
    begin
        TempTechnologyType.Reset();
        TempTechnologyType.DeleteAll();

        Rec.Reset();
        if Rec.FindSet() then
            repeat
                if Rec.Selected then begin
                    TempTechnologyType.Init();
                    TempTechnologyType := Rec;
                    TempTechnologyType.Insert();
                end;
            until Rec.Next() = 0;
    end;
}