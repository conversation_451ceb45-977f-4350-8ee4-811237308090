pageextension 50105 "Opportunity List Ext" extends "Opportunity List" // Page 5126
{
    layout
    {
        addafter("Calcd. Current Value (LCY)")
        {
            field("Internal_PO_Number__c"; Rec."Internal_PO_Number__c")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the internal PO number.';
            }
            field("Client_PO_Number__c"; Rec."Client_PO_Number__c")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the client PO number.';
            }
            field("Partner_Quote_Number__c"; Rec."Partner_Quote_Number__c")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the partner quote number.';
            }
            field("ERP_Quote_Number__c"; Rec."ERP_Quote_Number__c")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the ERP quote number.';
            }
            field("Invoice__c"; Rec."Invoice__c")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the invoice.';
            }
            field("Serial_Number__c"; Rec."Serial_Number__c")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the serial number';
            }
            field("Partner_For_The_Project__c"; Rec."Partner_For_The_Project__c")
            {
                ApplicationArea = All;
            }
            field("Amount"; Rec."Amount")
            {
                ApplicationArea = All;
            }
            field("Margin_Amount__c"; Rec."Margin_Amount__c")
            {
                ApplicationArea = All;
            }
            field("GrossProfit"; Rec."GrossProfit")
            {
                ApplicationArea = All;
            }
            field("Type_of_Project__c"; Rec."Type_of_Project__c")
            {
                ApplicationArea = All;
            }
        }
        moveafter("CurrSalesCycleStage"; "Contact No.")
        moveafter("Description"; "Contact Name")
        moveafter("Closed"; "Status")
    }

    actions
    {
        addlast(Processing)
        {
            action("Bulk Delete Opportunities")
            {
                ApplicationArea = All;
                Caption = 'Bulk Delete Selected Opportunities';
                ToolTip = 'Deletes all selected opportunities. This action cannot be undone.';
                Image = Delete;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                var
                    OpportunityRec: Record Opportunity;
                    SelectedCount: Integer;
                    DeletedCount: Integer;
                    ConfirmMsg: Label 'You have selected %1 opportunities. Are you sure you want to delete them? This action cannot be undone.';
                    SuccessMsg: Label '%1 opportunities were deleted.';
                    NothingSelectedMsg: Label 'No opportunities were selected.';
                begin
                    CurrPage.SetSelectionFilter(OpportunityRec);
                    SelectedCount := OpportunityRec.Count();

                    if SelectedCount = 0 then begin
                        Message(NothingSelectedMsg);
                        exit;
                    end;

                    if Confirm(ConfirmMsg, false, SelectedCount) then begin
                        if OpportunityRec.FindSet() then begin
                            repeat
                                if OpportunityRec.Delete(true) then
                                    DeletedCount += 1;
                            until OpportunityRec.Next() = 0;
                        end;
                        Message(SuccessMsg, DeletedCount);
                    end;
                end;
            }

            action("Search by Contact")
            {
                ApplicationArea = All;
                Caption = 'Search by Contact';
                ToolTip = 'Search and filter opportunities by contact';
                Image = ContactFilter;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                var
                    Contact: Record Contact;
                    ContactList: Page "Contact List";
                begin
                    ContactList.LookupMode := true;
                    if ContactList.RunModal() = ACTION::LookupOK then begin
                        ContactList.GetRecord(Contact);
                        Rec.FilterGroup(2);
                        Rec.SetRange("Contact No.", Contact."No.");
                        Rec.FilterGroup(0);
                    end;
                end;
            }

            action("Reset Filter")
            {
                ApplicationArea = All;
                Caption = 'Reset Filter';
                ToolTip = 'Clear all filters and view all opportunities';
                Image = ClearFilter;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    Rec.Reset();
                    Rec.FilterGroup(2);
                    Rec.SetRange("Contact No.");
                    Rec.FilterGroup(0);
                    CurrPage.Update(false);
                end;
            }
        }
    }
}